# ninja log v6
101715	187496	7757044920873885	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
67	8975	7757043904389972	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
190560	206916	****************	zlib/example64.exe	4a92267bf6f9e4ab
7119	12253	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
52	1931	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
5370	12251	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
387	9600	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
17228	106069	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
27	335	****************	CMakeFiles/clean.additional	baedbe210acaa455
883	10328	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
52	1931	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1365	11029	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
190557	205629	****************	zlib/minigzip.exe	b30485a50c6b3a85
1954	11661	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
190563	206178	****************	zlib/minigzip64.exe	4f10a1aa1eb3582e
12246	14821	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
2549	12245	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
190555	205716	****************	zlib/example.exe	61852f41d57c7507
7687	12254	****************	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
8321	12256	7757043986935172	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
8977	12258	7757043993497850	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
9600	12260	7757043999732191	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
18913	131550	7757044092862443	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
15985	61602	7757044063567418	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
10328	12965	7757044007013185	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
11029	13309	7757044014018314	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
11661	14016	7757044020336709	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
12246	14821	****************	zlib/zlib1rc.obj	5625031d43e5c7be
14822	24371	7757044051937533	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
18079	126943	7757044084521367	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
15279	43197	7757044056511494	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
14017	61596	7757044043888801	zlib/libzlibstatic.a	22b16c9e053b901
16603	101715	7757044069748455	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
19645	138841	7757044100178808	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
20521	142219	7757044108934294	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
24372	145587	7757044147442828	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
43198	154867	7757044335698249	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
18978	66031	7757414906250362	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
52	1931	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
61603	182809	7757044519750829	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
106069	188402	7757044964411550	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
126944	188674	7757045173168523	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
131551	188816	7757045219238897	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
138842	188469	7757045292148613	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
19279	62158	7757414909231732	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
142219	188289	7757045325917146	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
145587	189584	7757045359594098	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
188675	190554	7757045809130470	zlib/libzlib.dll	c70d7c7719fed2bf
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
188675	190554	7757045809130470	zlib/libzlib.dll.a	c70d7c7719fed2bf
52	1931	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
35738	46175	7757407769147136	OrderManager.exe	d9543c243f221849
45	4700	7757417592178324	build.ninja	497fbd042d0239fc
335	602	7757043044508358	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
288280	380363	7757046786532132	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
292422	380217	7757046827945260	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
19775	78896	7757414914204296	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
295780	380244	7757046861518504	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
217863	269350	7757046082351212	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
218822	277051	7757046091952819	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
218328	288280	7757046087004550	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
220262	311664	7757046106347224	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
269350	360517	7757046597230413	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
273759	364034	7757046641317331	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
219266	273758	7757046096385640	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
219715	287375	7757046100874863	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
20319	67893	7757414919722960	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
277051	375513	7757046674237544	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
290255	380072	7757046806275517	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
165137	190565	7756943652131103	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
287376	379105	7757046777478810	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
22431	57904	7757414940829354	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
21632	57240	7757414932816105	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
91	2169	7757424102753402	OrderManager_autogen/timestamp	21ad19f5d651830a
91	2169	7757424102753402	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
91	2169	7757424102753402	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
91	2169	7757424102753402	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2172	13448	7757424123500240	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
13449	25007	7757424236408383	OrderManager.exe	19cc37ce24d903f6
68	13406	7757428735566909	OrderManager_autogen/timestamp	21ad19f5d651830a
68	13406	7757428735566909	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
68	13406	7757428735566909	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
68	13406	7757428735566909	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
15426	38559	7757428763166443	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
13856	43215	7757428747401308	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
13409	46316	7757428742981481	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
14422	53721	7757428753045391	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
70	2169	7757430655667269	OrderManager_autogen/timestamp	21ad19f5d651830a
70	2169	7757430655667269	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
70	2169	7757430655667269	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
70	2169	7757430655667269	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2171	18514	7757430676651914	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
18516	28698	7757430840171177	OrderManager.exe	19cc37ce24d903f6
70	2841	7757437066904904	OrderManager_autogen/timestamp	21ad19f5d651830a
70	2841	7757437066904904	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
70	2841	7757437066904904	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
70	2841	7757437066904904	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2845	44599	7757437094710027	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
44600	52586	7757437512230406	OrderManager.exe	19cc37ce24d903f6
449	23187	7757438372004917	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
69	26598	7757438368224436	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
26598	38651	7757438633512365	OrderManager.exe	19cc37ce24d903f6
90	2855	7757441804380598	OrderManager_autogen/timestamp	21ad19f5d651830a
90	2855	7757441804380598	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
90	2855	7757441804380598	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
90	2855	7757441804380598	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2856	25537	7757441832035930	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
25537	33739	7757442058777034	OrderManager.exe	19cc37ce24d903f6
165	13125	7757443210628556	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
23	14366	7757443209112129	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
14366	20085	7757443352630054	OrderManager.exe	19cc37ce24d903f6
73	2094	7757447186616098	OrderManager_autogen/timestamp	21ad19f5d651830a
73	2094	7757447186616098	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
73	2094	7757447186616098	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
73	2094	7757447186616098	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2096	26598	7757447206911326	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
26600	35094	7757447451945198	OrderManager.exe	19cc37ce24d903f6
57	1735	7757450147931908	OrderManager_autogen/timestamp	21ad19f5d651830a
57	1735	7757450147931908	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
57	1735	7757450147931908	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
57	1735	7757450147931908	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1737	18040	7757450164728895	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
18041	24201	7757450327724196	OrderManager.exe	19cc37ce24d903f6
21	6722	7757522796531005	OrderManager_autogen/timestamp	21ad19f5d651830a
21	6722	7757522796531005	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
21	6722	7757522796531005	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
21	6722	7757522796531005	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
8466	30471	7757522817569234	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
8966	31861	7757522822557984	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
8046	31911	7757522813368857	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
6930	34649	7757522802213126	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
6723	36474	7757522800136972	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
7591	37482	7757522808765000	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
7245	42126	7757522805263900	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
42126	48054	7757523154086538	OrderManager.exe	19cc37ce24d903f6
23	14781	7757536421059857	OrderManager_autogen/timestamp	21ad19f5d651830a
23	14781	7757536421059857	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
23	14781	7757536421059857	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
23	14781	7757536421059857	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
17007	57992	7757536447951665	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
17623	58034	7757536454112863	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
15050	59678	7757536428383566	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
14783	64310	7757536425655424	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
