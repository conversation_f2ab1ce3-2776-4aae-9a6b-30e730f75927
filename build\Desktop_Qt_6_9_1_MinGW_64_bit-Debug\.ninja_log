# ninja log v6
9796	14307	7757707518321837	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
21	1826	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
16686	41119	****************	zlib/example64.exe	4a92267bf6f9e4ab
1299	2775	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
25	910	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1100	2775	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
169	1999	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
5714	10475	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
14	148	****************	CMakeFiles/clean.additional	baedbe210acaa455
354	2185	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
25	910	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
516	2360	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
16683	40839	****************	zlib/minigzip.exe	b30485a50c6b3a85
745	2544	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
16689	36857	****************	zlib/minigzip64.exe	4f10a1aa1eb3582e
2772	4197	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
932	2772	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
16680	40698	****************	zlib/example.exe	61852f41d57c7507
1463	2776	7757707434983939	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
1638	2776	7757707436738244	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1826	2776	7757707438615326	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
1999	2777	7757707440315782	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
6615	11699	7757707486509312	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
4887	9042	7757707469156674	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
2185	3090	7757707442206895	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
2360	3250	7757707443955606	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
2545	3612	7757707445802472	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
2772	4197	****************	zlib/zlib1rc.obj	5625031d43e5c7be
4197	8189	7757707462319598	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
6156	11103	7757707481917374	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
4493	8637	7757707465164674	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
3612	9038	7757707456482460	zlib/libzlibstatic.a	22b16c9e053b901
5245	9796	7757707472804661	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
7226	12527	7757707492617085	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
7708	13107	7757707497315852	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
8189	13629	7757707502250418	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
8637	14305	7757707506725199	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
6655	24800	7757719431744444	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
25	910	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
9042	14306	7757707510772678	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
10475	14308	7757707525103865	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
11103	14308	7757707531383388	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
11699	14312	7757707537349825	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
12527	14701	7757707545616541	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
6928	23217	7757719434487195	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
13107	15388	7757707551413003	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
13630	16047	7757707556656733	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
14309	16680	7757707587002167	zlib/libzlib.dll	c70d7c7719fed2bf
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
14309	16680	7757707587002167	zlib/libzlib.dll.a	c70d7c7719fed2bf
25	910	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
27807	36071	7757727454882052	OrderManager.exe	19cc37ce24d903f6
45	4700	7757739789304003	build.ninja	497fbd042d0239fc
149	278	7757707406481485	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
126165	148256	7757708681947878	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
127654	148114	7757708696877305	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
911	27807	7757727185979876	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
128090	148324	7757708701249249	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
************	7757707908315281	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
218822	277051	7757046091952819	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
************	7757707911614277	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
************	7757707925727771	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
************	7757707932387766	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
101515	146098	7757708435455907	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
49550	105957	7757707915853564	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
************	7757707920733686	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
1093	20597	7757727187822278	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
105958	145523	7757708479885732	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
127083	148037	7757708691159106	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
165137	190565	7756943652131103	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
125215	148259	7757708672465312	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
129135	151353	7757708711694052	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
128547	150529	7757708705802857	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
26	13121	7757739921692454	OrderManager_autogen/timestamp	21ad19f5d651830a
26	13121	7757739921692454	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
26	13121	7757739921692454	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
26	13121	7757739921692454	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
13463	49219	7757739933410027	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
14477	49512	7757739943549398	CMakeFiles/OrderManager.dir/src/login/async_login_manager.cpp.obj	82eb7d30a028e620
13123	51123	7757739930017793	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
13979	56768	7757739938564631	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
56768	63304	7757740366435486	OrderManager.exe	5cfaa9c56e32389d
21	786	7757743886284489	OrderManager_autogen/timestamp	21ad19f5d651830a
21	786	7757743886284489	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
21	786	7757743886284489	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
21	786	7757743886284489	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
787	8316	7757743893950766	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
8316	15745	7757743969310667	OrderManager.exe	5cfaa9c56e32389d
20	860	7757748243148562	OrderManager_autogen/timestamp	21ad19f5d651830a
20	860	7757748243148562	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
20	860	7757748243148562	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
20	860	7757748243148562	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
861	9901	7757748251670440	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
9902	16870	7757748341978812	OrderManager.exe	5cfaa9c56e32389d
23	994	7757750122282409	OrderManager_autogen/timestamp	21ad19f5d651830a
23	994	7757750122282409	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
23	994	7757750122282409	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
23	994	7757750122282409	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
995	12083	7757750131964830	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
12084	18744	7757750242903257	OrderManager.exe	5cfaa9c56e32389d
32	823	7757755147753010	OrderManager_autogen/timestamp	21ad19f5d651830a
32	823	7757755147753010	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
32	823	7757755147753010	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
32	823	7757755147753010	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
823	10606	7757755155588116	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
10607	18441	7757755253506913	OrderManager.exe	5cfaa9c56e32389d
99	3022	7757758423049395	OrderManager_autogen/timestamp	21ad19f5d651830a
99	3022	7757758423049395	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
99	3022	7757758423049395	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
99	3022	7757758423049395	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
3024	26843	7757758452291704	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
26844	33782	7757758690478608	OrderManager.exe	5cfaa9c56e32389d
21	868	7757765221338992	OrderManager_autogen/timestamp	21ad19f5d651830a
21	868	7757765221338992	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
21	868	7757765221338992	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
21	868	7757765221338992	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
869	10983	7757765229816633	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
10983	18386	7757765330954400	OrderManager.exe	5cfaa9c56e32389d
154	5183	7757768967438728	OrderManager_autogen/timestamp	21ad19f5d651830a
154	5183	7757768967438728	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
154	5183	7757768967438728	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
154	5183	7757768967438728	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
5186	59706	7757769017743476	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
59706	69377	7757769562873284	OrderManager.exe	5cfaa9c56e32389d
22	1067	7757774712409897	OrderManager_autogen/timestamp	21ad19f5d651830a
22	1067	7757774712409897	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
22	1067	7757774712409897	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
22	1067	7757774712409897	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1068	12636	7757774723007424	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
1216	19442	7757774724405879	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
19442	26523	7757774906747856	OrderManager.exe	5cfaa9c56e32389d
