# ninja log v6
12354	16669	****************	zlib/CMakeFiles/zlib.dir/trees.c.obj	447e431a4948446d
22	1951	****************	zlib/CMakeFiles/zlibstatic.dir/adler32.c.obj	f522e18397b115ed
8287	13025	****************	zlib/CMakeFiles/zlib.dir/gzclose.c.obj	11c9784c4bd74ca7
40291	71918	****************	CMakeFiles/OrderManager.dir/src/core/models/account.cpp.obj	30ef1af5bd32983e
163	2336	****************	zlib/CMakeFiles/zlibstatic.dir/compress.c.obj	48a0c52b3da0185b
12349	42361	****************	OrderManager_autogen/timestamp	21ad19f5d651830a
1069	4528	****************	zlib/CMakeFiles/zlibstatic.dir/gzread.c.obj	c06e4ad4f36042aa
19005	28230	****************	zlib/example64.exe	4a92267bf6f9e4ab
1273	4529	****************	zlib/CMakeFiles/zlibstatic.dir/gzwrite.c.obj	b66e792ce088242e
304	2801	****************	zlib/CMakeFiles/zlibstatic.dir/crc32.c.obj	4b3268fdeca9594b
14	168	****************	CMakeFiles/clean.additional	baedbe210acaa455
445	3488	****************	zlib/CMakeFiles/zlibstatic.dir/deflate.c.obj	7d4ded656980f6a1
12349	42361	****************	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
631	3997	****************	zlib/CMakeFiles/zlibstatic.dir/gzclose.c.obj	eb3ad0bfda399236
19001	28609	****************	zlib/minigzip.exe	b30485a50c6b3a85
877	4523	****************	zlib/CMakeFiles/zlibstatic.dir/gzlib.c.obj	18208857496f6fd7
4523	6665	7757628414762605	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/zlib/zlib1rc.obj	5625031d43e5c7be
19008	28086	7757628538234319	zlib/minigzip64.exe	4f10a1aa1eb3582e
1470	4531	7757628362845574	zlib/CMakeFiles/zlibstatic.dir/inflate.c.obj	e13fb17340c62a6c
18997	28326	7757628538057718	zlib/example.exe	61852f41d57c7507
1680	4532	7757628364843814	zlib/CMakeFiles/zlibstatic.dir/infback.c.obj	3eba6382827f66fe
1952	4533	7757628367674272	zlib/CMakeFiles/zlibstatic.dir/inftrees.c.obj	62539bd8caba5658
2336	4535	7757628371513341	zlib/CMakeFiles/zlibstatic.dir/inffast.c.obj	a345386ff2706ef5
2125	44158	7755097649365346	CMakeFiles/OrderManager.dir/src/integration/modern_integration.cpp.obj	93cc302079915e2b
2802	5105	7757628376169891	zlib/CMakeFiles/zlibstatic.dir/trees.c.obj	f5dba1b74cacaf12
7403	11809	7757628422177266	zlib/CMakeFiles/zlib.dir/crc32.c.obj	9d81cc3f4d2b4176
9258	14235	7757628440679179	zlib/CMakeFiles/zlib.dir/gzread.c.obj	b20b4e642e753667
3488	5446	7757628383037804	zlib/CMakeFiles/zlibstatic.dir/uncompr.c.obj	bddd5ec36b6795fe
3997	5990	7757628388125837	zlib/CMakeFiles/zlibstatic.dir/zutil.c.obj	5a2fb8d71b511e64
4523	6665	7757628414762605	zlib/zlib1rc.obj	5625031d43e5c7be
6666	10733	7757628414813734	zlib/CMakeFiles/zlib.dir/adler32.c.obj	c3b4b45d37e3fb0d
181950	247439	7754819167361462	CMakeFiles/OrderManager.dir/service_container.cpp.obj	1cecbdc6d6e67c64
6973	11196	7757628417886626	zlib/CMakeFiles/zlib.dir/compress.c.obj	dca58b9d052ae50b
8800	13633	7757628436159155	zlib/CMakeFiles/zlib.dir/gzlib.c.obj	2cbb3770f01112f2
7814	12353	7757628426244859	zlib/CMakeFiles/zlib.dir/deflate.c.obj	fc038e22e8d52672
5990	12349	7757628408048939	zlib/libzlibstatic.a	22b16c9e053b901
9835	14841	7757628446500199	zlib/CMakeFiles/zlib.dir/gzwrite.c.obj	316c684add36a56a
10273	15445	7757628450889377	zlib/CMakeFiles/zlib.dir/inflate.c.obj	bf13e58d5b3961b4
10733	16025	7757628455485765	zlib/CMakeFiles/zlib.dir/infback.c.obj	f4170c545da9380
119934	231176	7754818547195106	CMakeFiles/OrderManager.dir/network_client.cpp.obj	5efdf4f57ec48db9
11196	16666	7757628460110726	zlib/CMakeFiles/zlib.dir/inftrees.c.obj	afa076acbf5b0447
11810	16668	7757628466246811	zlib/CMakeFiles/zlib.dir/inffast.c.obj	7d6e9b320b5e1ffe
12349	42361	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
42361	108180	7757628771663482	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
13025	16670	7757628478344960	zlib/CMakeFiles/zlib.dir/uncompr.c.obj	3aaf688a7f365032
4604	60089	7754908922675662	CMakeFiles/OrderManager.dir/orderapi.cpp.obj	b4308165159a82f
13633	16672	7757628484341251	zlib/CMakeFiles/zlib.dir/zutil.c.obj	5882baff379b488e
14235	16677	7757628490508372	zlib/CMakeFiles/example.dir/test/example.c.obj	537cb4e3373a0e57
42554	107196	7757628773665086	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
128654	232204	7754818634395641	CMakeFiles/OrderManager.dir/json_parser.cpp.obj	e6d02183e44aeb8f
14841	17089	7757628496561368	zlib/CMakeFiles/minigzip.dir/test/minigzip.c.obj	7ae3fe93430fa513
15445	17699	7757628502603868	zlib/CMakeFiles/example64.dir/test/example.c.obj	5fbd94de175120ff
16025	18298	7757628508408787	zlib/CMakeFiles/minigzip64.dir/test/minigzip.c.obj	db18db776bd12c8f
94679	220192	7754818294646091	CMakeFiles/OrderManager.dir/simple_logger.cpp.obj	e54af13d31d8f10f
16672	18996	7757628537947621	zlib/libzlib.dll	c70d7c7719fed2bf
16672	18996	7757628537947621	zlib/libzlib.dll.a	c70d7c7719fed2bf
3913	71181	7754908915703074	CMakeFiles/OrderManager.dir/mainwindow.cpp.obj	9b7615c4e9899990
12349	42361	****************	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51587	193646	7754817863727967	CMakeFiles/OrderManager.dir/filterworker.cpp.obj	180146051c6ec444
5484	51615	7754908933435316	CMakeFiles/OrderManager.dir/ultrafasttls.cpp.obj	9ba90bdafac4c590
130131	136305	7757629649307207	OrderManager.exe	19cc37ce24d903f6
45	4700	7757417592178324	build.ninja	497fbd042d0239fc
335	602	7757043044508358	clean	577b940aee94fbd7
109269	231173	7754818440550909	CMakeFiles/OrderManager.dir/error_handler.cpp.obj	d9095fc1e9e81361
141919	245469	7754818767048056	CMakeFiles/OrderManager.dir/authentication_service.cpp.obj	22d1d11473c21309
124266	231178	7754818590514734	CMakeFiles/OrderManager.dir/encryption_service.cpp.obj	f8238a953d9d6499
106670	123654	7757629414841590	CMakeFiles/OrderManager.dir/legacy/services/service_container.cpp.obj	1939c410dd52b724
************	7754817890328500	CMakeFiles/OrderManager.dir/ultrafasttls_debug_monitor.cpp.obj	e9823b695ea365cc
209397	255577	7754819441832514	CMakeFiles/OrderManager.dir/response_processor.cpp.obj	e837ab9a2c62cd48
193646	253291	7754819284318579	CMakeFiles/OrderManager.dir/request_builder.cpp.obj	9107ea44c00293f3
220193	256526	7754819549787515	CMakeFiles/OrderManager.dir/business_logic.cpp.obj	e04d3b1651fbef92
107635	126980	7757629424490167	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
108181	126869	7757629429951705	CMakeFiles/OrderManager.dir/src/core/utils/logger.cpp.obj	612400e9f0cf2cee
************	7757628776343859	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
43432	96078	7757628782457858	CMakeFiles/OrderManager.dir/legacy/workers/filterworker.cpp.obj	826af9fb1fa93399
218822	277051	7757046091952819	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls_debug_monitor.cpp.obj	340861ce9eed5eb6
************	7757628786726035	CMakeFiles/OrderManager.dir/legacy/network/ultrafasttls.cpp.obj	4857e80d89c1afdb
************	7757628802350504	CMakeFiles/OrderManager.dir/legacy/utils/simple_logger.cpp.obj	6044af7adc933d63
************	7757628809039031	CMakeFiles/OrderManager.dir/legacy/utils/error_handler.cpp.obj	cf7350196749cff2
************	7757629308925120	CMakeFiles/OrderManager.dir/legacy/utils/json_parser.cpp.obj	e9f5303b6da1157c
44322	104984	7757628791361464	CMakeFiles/OrderManager.dir/legacy/network/request_builder.cpp.obj	7c1b512cf99168e2
************	7757628796434838	CMakeFiles/OrderManager.dir/legacy/network/response_processor.cpp.obj	e7764d80e2adeeee
************	7757628778934207	CMakeFiles/OrderManager.dir/legacy/api/orderapi.cpp.obj	9f06947dd242fb40
104985	124203	7757629397974290	CMakeFiles/OrderManager.dir/legacy/services/encryption_service.cpp.obj	7998b014f1b37ece
107196	125739	7757629419992354	CMakeFiles/OrderManager.dir/legacy/services/business_logic.cpp.obj	1cc6f467453735f9
165137	190565	7756943652131103	CMakeFiles/OrderManager.dir/legacy/network/network_client.cpp.obj	9f436df8eb47560a
105894	124070	7757629407073732	CMakeFiles/OrderManager.dir/legacy/services/authentication_service.cpp.obj	eddb7b5dd58046a6
109436	130131	7757629442498546	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
108742	129557	7757629435554346	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
50574	69200	7755128466411942	CMakeFiles/OrderManager.dir/src/core/controllers/app_controller.cpp.obj	fa3260da5e747e99
49543	76213	7755128456102014	CMakeFiles/OrderManager.dir/src/core/services/order_service.cpp.obj	65f0a8514c0d6d42
24	798	7757634003356601	OrderManager_autogen/timestamp	21ad19f5d651830a
24	798	7757634003356601	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
24	798	7757634003356601	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
24	798	7757634003356601	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
799	17937	7757634011070458	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
17937	25322	7757634182483964	OrderManager.exe	19cc37ce24d903f6
235	4174	7757636278134185	OrderManager_autogen/timestamp	21ad19f5d651830a
235	4174	7757636278134185	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
235	4174	7757636278134185	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
235	4174	7757636278134185	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
4177	20687	7757636317540016	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
20687	27355	7757636482586203	OrderManager.exe	19cc37ce24d903f6
39	2109	7757640621464143	OrderManager_autogen/timestamp	21ad19f5d651830a
39	2109	7757640621464143	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
39	2109	7757640621464143	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
39	2109	7757640621464143	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2111	28091	7757640642136103	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
28092	35252	7757640901978521	OrderManager.exe	19cc37ce24d903f6
20	904	7757647186151581	OrderManager_autogen/timestamp	21ad19f5d651830a
20	904	7757647186151581	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
20	904	7757647186151581	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
20	904	7757647186151581	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
905	12964	7757647194906671	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
12964	19533	7757647315582999	OrderManager.exe	19cc37ce24d903f6
23	809	7757653287050205	OrderManager_autogen/timestamp	21ad19f5d651830a
23	809	7757653287050205	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
23	809	7757653287050205	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
23	809	7757653287050205	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
810	13996	7757653295048687	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
13996	20369	7757653426878574	OrderManager.exe	19cc37ce24d903f6
74	3171	7757657457602717	OrderManager_autogen/timestamp	21ad19f5d651830a
74	3171	7757657457602717	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
74	3171	7757657457602717	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
74	3171	7757657457602717	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
3173	22369	7757657488582465	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
22369	28991	7757657680541868	OrderManager.exe	19cc37ce24d903f6
24	3998	7757664866001879	OrderManager_autogen/timestamp	21ad19f5d651830a
24	3998	7757664866001879	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
24	3998	7757664866001879	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
24	3998	7757664866001879	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
4519	19225	7757664873870153	CMakeFiles/OrderManager.dir/src/config/app_config.cpp.obj	2be737d65681ef59
4691	23812	7757664875507472	CMakeFiles/OrderManager.dir/src/network/engines/ultrafasttls_adapter.cpp.obj	efc1a9f4e8a98669
4834	24776	7757664877021197	CMakeFiles/OrderManager.dir/src/integration/legacy_api_adapter.cpp.obj	52c2213937326bdc
4131	27697	7757664869846757	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
4000	29439	7757664868678959	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
4352	34686	7757664872181206	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
34686	40890	7757665175537207	OrderManager.exe	19cc37ce24d903f6
40	9574	7757672788726636	OrderManager_autogen/timestamp	21ad19f5d651830a
40	9574	7757672788726636	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
40	9574	7757672788726636	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
40	9574	7757672788726636	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
9849	42676	7757672796324964	CMakeFiles/OrderManager.dir/main.cpp.obj	eb2ac27ff84ff740
9579	44888	7757672793617317	CMakeFiles/OrderManager.dir/OrderManager_autogen/mocs_compilation.cpp.obj	1a6e6186093c6553
10264	52058	7757672800484263	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
52058	60250	7757673218409454	OrderManager.exe	19cc37ce24d903f6
51	1721	7757676630403909	OrderManager_autogen/timestamp	21ad19f5d651830a
51	1721	7757676630403909	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
51	1721	7757676630403909	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
51	1721	7757676630403909	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1722	24415	7757676647105908	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
24415	35789	7757676874038912	OrderManager.exe	19cc37ce24d903f6
25	866	7757680207782283	OrderManager_autogen/timestamp	21ad19f5d651830a
25	866	7757680207782283	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
25	866	7757680207782283	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
25	866	7757680207782283	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
867	15204	7757680216187699	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
15205	21665	7757680359568045	OrderManager.exe	19cc37ce24d903f6
49	1661	7757682663814498	OrderManager_autogen/timestamp	21ad19f5d651830a
49	1661	7757682663814498	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
49	1661	7757682663814498	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
49	1661	7757682663814498	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
1663	23183	7757682680008584	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
23184	35197	7757682895214497	OrderManager.exe	19cc37ce24d903f6
23	771	7757685719827551	OrderManager_autogen/timestamp	21ad19f5d651830a
23	771	7757685719827551	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
23	771	7757685719827551	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
23	771	7757685719827551	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
772	11660	7757685727272442	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
11660	18372	7757685836187888	OrderManager.exe	19cc37ce24d903f6
49	2095	7757689126454702	OrderManager_autogen/timestamp	21ad19f5d651830a
49	2095	7757689126454702	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
49	2095	7757689126454702	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
49	2095	7757689126454702	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
2098	39407	7757689146946356	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
39407	48229	7757689520034426	OrderManager.exe	19cc37ce24d903f6
26	885	7757692636455825	OrderManager_autogen/timestamp	21ad19f5d651830a
26	885	7757692636455825	OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
26	885	7757692636455825	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/timestamp	21ad19f5d651830a
26	885	7757692636455825	C:/eee/cc/build/Desktop_Qt_6_9_1_MinGW_64_bit-Debug/OrderManager_autogen/mocs_compilation.cpp	21ad19f5d651830a
886	13631	7757692645098531	CMakeFiles/OrderManager.dir/src/ui/mainwindow.cpp.obj	60e76c7693d9cc6a
13632	20060	7757692772455693	OrderManager.exe	19cc37ce24d903f6
