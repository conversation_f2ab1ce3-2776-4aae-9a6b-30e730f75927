# 🔍 网络问题诊断指南

## 🚨 **当前问题症状**

1. **主账号响应为空** - `⚠️ 响应为空`
2. **JSON解析错误** - `illegal value`  
3. **子账号网络请求失败** - `网络请求失败`

## 🛠️ **已启用的调试措施**

### 1. **UltraFastTLS调试日志**
```cpp
// 主账号和子账号都已开启详细日志
m_ultraFastTLS->setQuietMode(false);
m_subAccountUltraFastTLS->setQuietMode(false);
```

### 2. **请求过程日志**
```cpp
emit debugLog(QString("🚀 开始执行子账号请求: %1").arg(fullUrl));
emit debugLog(QString("📥 子账号请求响应长度: %1").arg(response.length()));
```

## 🔍 **诊断步骤**

### 第一步：重新编译运行
1. 重新编译项目
2. 启动程序
3. 批量登录账号
4. 点击"开始刷新"
5. 观察详细日志输出

### 第二步：查看关键日志
重点关注以下日志信息：

#### A. **SSL连接建立**
```
🔗 正在建立SSL连接到: server.dailiantong.com.cn:443
✅ SSL连接建立成功
❌ SSL连接失败: [错误信息]
```

#### B. **代理连接**
```
🔗 UltraFastTLS代理已设置: proxy.com:8080 (http)
✅ HTTP代理连接成功: server.dailiantong.com.cn:443
❌ HTTP代理连接失败: [错误信息]
```

#### C. **HTTP请求响应**
```
🚀 开始执行子账号请求: https://server.dailiantong.com.cn/...
📥 子账号请求响应长度: 1234
```

### 第三步：可能的问题和解决方案

#### 问题1：SSL握手失败
**症状**：看到SSL连接失败的日志
**原因**：可能是我们的性能优化影响了SSL初始化
**解决**：检查SSL上下文是否正确初始化

#### 问题2：代理连接失败  
**症状**：看到代理连接失败的日志
**原因**：代理服务器可能不可用或认证失败
**解决**：
1. 检查代理服务器是否正常
2. 验证代理认证信息
3. 尝试直连模式

#### 问题3：连接池问题
**症状**：请求卡住，无响应
**原因**：连接池管理可能有问题
**解决**：检查borrowConnection和returnConnection逻辑

#### 问题4：内存管理问题
**症状**：程序崩溃或异常
**原因**：UltraFastTLS实例管理可能有问题
**解决**：检查实例创建和销毁逻辑

## 🚀 **快速修复尝试**

### 方案1：回退到简单模式
如果问题严重，可以临时回退：

```cpp
// 在OrderAPI构造函数中
m_subAccountUltraFastTLS->setQuietMode(true);  // 关闭调试日志
```

### 方案2：强制重新初始化
```cpp
// 在refreshOrders开始时
if (m_subAccountUltraFastTLS) {
    delete m_subAccountUltraFastTLS;
    m_subAccountUltraFastTLS = new UltraFastTLS(this);
    // 重新配置...
}
```

### 方案3：检查网络引擎选择
确保使用正确的网络引擎：
```cpp
LOG_DEBUG(LogCategory::ORDER, QString("使用 %1 引擎刷新订单")
          .arg(m_networkEngine == NetworkEngine::ULTRA_FAST_TLS ? "UltraFastTLS" : "curl"));
```

## 📋 **日志收集清单**

运行程序后，请收集以下日志信息：

### ✅ **必须收集的日志**
- [ ] SSL连接建立过程
- [ ] 代理配置设置过程  
- [ ] HTTP请求发送过程
- [ ] 响应接收过程
- [ ] 错误信息详情

### ✅ **有用的额外信息**
- [ ] 程序启动日志
- [ ] 账号登录日志
- [ ] 内存使用情况
- [ ] CPU使用情况

## 🎯 **预期结果**

正常情况下应该看到：
```
🔗 为账号配置代理: proxy.com:8080 (http)
🔗 UltraFastTLS代理已设置: proxy.com:8080 (http)
✅ HTTP代理连接成功: server.dailiantong.com.cn:443
🚀 开始执行子账号请求: https://server.dailiantong.com.cn/API/AppService.ashx?Action=LevelOrderList
📥 子账号请求响应长度: 1234
✅ 订单刷新成功，获得 X 个订单
```

## 🆘 **如果问题持续**

如果启用调试日志后仍然无法定位问题，可以考虑：

1. **临时回退**：恢复到修改前的版本
2. **分步测试**：先测试无代理的账号
3. **单独测试**：只测试一个账号的刷新
4. **网络抓包**：使用Wireshark等工具分析网络流量

---

**重要**：请先重新编译运行，然后将详细的日志输出贴出来，这样我们就能准确定位问题所在！
