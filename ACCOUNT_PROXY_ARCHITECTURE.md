# 🏗️ 各账号独立代理架构说明

## ✅ **现在是各账号各自代理！**

经过修复，现在每个账号都有独立的代理配置，不会相互干扰。

## 🎯 **架构设计**

### 修复前的问题
```
❌ 所有子账号共享一个 m_subAccountUltraFastTLS 实例
❌ 代理配置会相互覆盖
❌ 最后设置的代理会影响所有账号
```

### 修复后的架构
```
✅ 每个账号有独立的 OrderAPI 实例
✅ 每个 OrderAPI 有独立的 UltraFastTLS 实例
✅ 每个 UltraFastTLS 有独立的代理配置和连接池
```

## 🔧 **实现细节**

### 1. 账号结构
```cpp
struct AccountInfo {
    QString username;
    QString password;
    QString proxyHost;      // 独立的代理主机
    int proxyPort = 0;      // 独立的代理端口
    QString proxyType = "http";
    QString proxyUser;      // 独立的代理用户名
    QString proxyPass;      // 独立的代理密码
    
    OrderAPI *api = nullptr; // 🔑 每个账号独立的API实例
    
    // 析构函数确保API实例被正确清理
    ~AccountInfo() {
        if (api) {
            api->deleteLater();
            api = nullptr;
        }
    }
};
```

### 2. OrderAPI实例创建
```cpp
// 在批量登录时，为每个账号创建独立的OrderAPI
acc.api = new OrderAPI(this);

// 每个OrderAPI内部都有独立的UltraFastTLS实例
m_subAccountUltraFastTLS = new UltraFastTLS(this);
```

### 3. 代理配置设置
```cpp
// 刷新订单时，为当前账号设置独立的代理
account.api->refreshOrders(gameId, account.token, account.userId,
                          account.proxyHost, account.proxyPort,    // 使用账号自己的代理
                          account.proxyType, account.proxyUser, account.proxyPass,
                          priceStr, focusFlag);

// 在OrderAPI内部
if (!proxyHost.isEmpty() && proxyPort > 0) {
    m_subAccountUltraFastTLS->setProxy(proxyHost, proxyPort, proxyType, proxyUser, proxyPass);
} else {
    m_subAccountUltraFastTLS->clearProxy();
}
```

## 📊 **架构图**

```
MainWindow
├── m_api (主账号OrderAPI)
│   ├── m_ultraFastTLS (主账号专用)
│   └── m_subAccountUltraFastTLS (主账号子功能)
│
├── account1.api (OrderAPI实例1)
│   └── m_subAccountUltraFastTLS (独立实例1)
│       ├── 代理配置: proxy1.com:8080
│       └── 连接池: 独立管理
│
├── account2.api (OrderAPI实例2)
│   └── m_subAccountUltraFastTLS (独立实例2)
│       ├── 代理配置: proxy2.com:8081
│       └── 连接池: 独立管理
│
└── account3.api (OrderAPI实例3)
    └── m_subAccountUltraFastTLS (独立实例3)
        ├── 代理配置: 直连模式
        └── 连接池: 独立管理
```

## 🚀 **性能优势**

### 1. 真正的并行处理
- 每个账号使用独立的网络连接
- 不同代理的连接不会相互干扰
- 连接池按账号独立管理

### 2. 故障隔离
- 一个账号的代理故障不影响其他账号
- 每个账号可以使用不同类型的代理（HTTP/SOCKS5）
- 独立的错误处理和重试机制

### 3. 配置灵活性
```
账号1: HTTP代理 proxy1.com:8080
账号2: SOCKS5代理 proxy2.com:1080  
账号3: 直连模式
账号4: HTTP代理 proxy3.com:8081 (带认证)
```

## 🔍 **验证方法**

### 1. 日志验证
启用调试模式后，可以看到每个账号的独立代理配置：
```
🔗 为账号配置代理: proxy1.com:8080 (http)
🔗 为账号配置代理: proxy2.com:1080 (socks5)
🔗 账号使用直连模式
```

### 2. 实例地址验证
```cpp
// 测试方法会显示每个账号的UltraFastTLS实例地址
QString OrderAPI::getCurrentProxyInfo() const {
    return QString("代理已配置 (实例: %1)")
           .arg(reinterpret_cast<quintptr>(m_subAccountUltraFastTLS), 0, 16);
}
```

### 3. 程序内置测试
调用 `testProxyFunctionality()` 方法可以验证：
- 每个账号的代理配置独立性
- UltraFastTLS实例的独立性
- 连接池的隔离性

## 📋 **使用示例**

### 账号文件格式
```
# 每行一个账号，各自使用不同的代理
user1,pass1,proxy1.com,8080,http,proxyuser1,proxypass1
user2,pass2,proxy2.com,1080,socks5,proxyuser2,proxypass2
user3,pass3,,,,,  # 直连模式
user4,pass4,proxy3.com,8081,http,proxyuser3,proxypass3
```

### 运行效果
```
[12:34:56] 账号[user1]: 代理已配置 (实例: 7ff1234567890) -> 代理: proxy1.com:8080
[12:34:57] 账号[user2]: 代理已配置 (实例: 7ff1234567abc) -> 代理: proxy2.com:1080
[12:34:58] 账号[user3]: 直连模式 (实例: 7ff1234567def) -> 直连模式
[12:34:59] 账号[user4]: 代理已配置 (实例: 7ff1234567fed) -> 代理: proxy3.com:8081
```

## 🎉 **总结**

**✅ 现在确实是各账号各自代理！**

- 每个账号有独立的OrderAPI实例
- 每个OrderAPI有独立的UltraFastTLS实例  
- 每个UltraFastTLS有独立的代理配置
- 连接池按账号完全隔离
- 支持不同账号使用不同类型的代理
- 故障隔离，一个账号的问题不影响其他账号

这个架构确保了真正的多账号并行处理，每个账号都可以使用自己专属的代理配置！
