#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTableWidget>
#include <QLineEdit>
#include <QPushButton>

#include <QTextEdit>
#include <QSpinBox>
#include <QThread>
#include <QMutex>
#include <QTimer>
#include <QList>
#include <QSet>
#include <QHash>
#include <QJsonObject>
#include <orderapi.h>
#include "ui_mainwindow.h"
#include <QFutureWatcher>
#include <QtConcurrent>
#include <QQueue>
#include <QEventLoop>
#include <QAtomicInt>

// 前向声明
class FilterWorker;

QT_BEGIN_NAMESPACE
class QTableWidget;
class QLineEdit;
class QPushButton;
class QComboBox;
class QTextEdit;
class QSpinBox;
class QCheckBox;
class QPlainTextEdit;
QT_END_NAMESPACE

struct AccountInfo {
    QString username;
    QString password;
    QString proxyHost;
    int proxyPort = 0;
    QString proxyType = "http"; // "http" or "socks5"
    QString proxyUser;
    QString proxyPass;
    QString token;
    QString userId;
    bool isLoggedIn = false;
    QString uid; // 新增：登录返回的UID，用于加密支付密码
    OrderAPI *api = nullptr; // 每个账号独立的API实例
    bool loginSent = false;  // 已经发送登录请求标志，避免重复提交

    // 析构函数确保API实例被正确清理
    ~AccountInfo() {
        if (api) {
            api->deleteLater();
            api = nullptr;
        }
    }

    // 拷贝构造函数
    AccountInfo(const AccountInfo& other)
        : username(other.username), password(other.password)
        , proxyHost(other.proxyHost), proxyPort(other.proxyPort)
        , proxyType(other.proxyType), proxyUser(other.proxyUser), proxyPass(other.proxyPass)
        , token(other.token), userId(other.userId), isLoggedIn(other.isLoggedIn)
        , uid(other.uid), api(nullptr), loginSent(other.loginSent) {
        // API实例不拷贝，需要重新创建
    }

    // 赋值操作符
    AccountInfo& operator=(const AccountInfo& other) {
        if (this != &other) {
            username = other.username;
            password = other.password;
            proxyHost = other.proxyHost;
            proxyPort = other.proxyPort;
            proxyType = other.proxyType;
            proxyUser = other.proxyUser;
            proxyPass = other.proxyPass;
            token = other.token;
            userId = other.userId;
            isLoggedIn = other.isLoggedIn;
            uid = other.uid;
            loginSent = other.loginSent;
            // API实例不拷贝，保持原有的
        }
        return *this;
    }
};

struct OrderInfo {
    QString orderId;
    QString title;
    QString timeLimit; // 限时
    QString price;
    QString createUser;
    QString zoneServer;
    QString status;
    QString timestamp;
    QString publishUserId; // 发单人ID
};

struct MainAccountInfo {
    QString username;
    QString password;
    QString proxyHost;
    int proxyPort = 0;
    QString proxyUser;
    QString proxyPass;
    QString token;
    QString userId;
    bool isLoggedIn = false;
    QString uid; // 登录返回的UID
};

// ===== 自动接单排队 =====
struct AcceptEntry {
    QJsonObject orderObj;
    int attempts = 0;
    bool manual = false; // true=手动双击触发，false=自动队列
    int concurrentRequests = 0; // 记录当前正在进行的并发请求数量
    bool initialBurst = true; // 标记是否是首次并发请求
    bool supplementalSent = false; // 标记是否已发送补充请求
};

struct RefreshParseResult {
    QVector<OrderInfo> newOrders;        // 需要插入表格
    QVector<QJsonObject> autoAcceptObjs; // 需要自动接单
    QSet<QString> newSeenIds;            // 新增的已见订单ID
    QHash<QString,double> newPriceMap;   // 更新后的最新价格
    int newCount = 0;                    // 新增表行数（统计用）
    QString key;                         // 所属账号键
};

// 高级关键词分组优化
struct KeywordPriceEntry {
    QString keyword;
    double minPrice;
};

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    enum ResultCol {
        RES_SEQ = 0,
        RES_ORDERID,
        RES_TITLE,
        RES_PRICE,
        RES_TIMELIMIT,
        RES_CREATOR,
        RES_ZONE,
        RES_TIME,
        RES_STATUS,
        RES_UID
    };

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void importAccountsFromFile(const QString &fileName);

signals:
    void startExtraExclusionFilter(const QString &title);
    void allFiltersFinished();

private slots:
    void onLoginClicked();
    void onBatchLoginClicked();
    void onRefreshClicked();
    void onStopClicked();
    void onLoadAccountsClicked();
    void onClearLogClicked();
    
    void onLoginResult(bool success, const QString &message, const QString &/*token*/);
    void onOrderRefreshResult(bool success, const QString &message, const QJsonArray &orders, int recordCount);
    void onNetworkError(const QString &error);
    void onAccountParseFinished();
    
    void onRefreshTimer();
    void onMainAccountRefreshClicked();
    void onUserInfoResult(bool success, const QString &message, const QJsonObject &info);
    // 右键菜单
    void onOrderTableContextMenu(const QPoint &pos);
    void onClearNewOrders();
    // 新增：接单结果
    void onOrderAcceptResult(bool success, const QString &message);
    void loadSettings();
    void saveSettings();
    
    // 额外排除关键词处理
    void onExtraExclusionResult(bool result);
    void checkAllFiltersFinished();


    // void onTestNewNetworkEngine();  // 测试新网络引擎架构 - 暂时注释掉

private:
    Ui::MainWindow *ui; // 指向Designer生成的UI
    void loadAccountsFromFile();
    void addLogMessage(const QString &message);
    void updateAccountTable();
    void updateOrderTable();
    void startRefreshTimer();
    void stopRefreshTimer();
    void setupWorkerThreads();
    void createNetworkEngineControls();

    // 辅助函数
    bool performExtraExclusionCheck(const QString& title);
    
    // UI组件
    QTableWidget *m_accountTable;
    QTableWidget *m_orderTable;
    QTableWidget *m_resultTable;
    QPlainTextEdit *m_logText;
    QLineEdit *m_usernameEdit;
    QLineEdit *m_passwordEdit;
    QComboBox *m_gameCombo;
    QSpinBox *m_pageSizeSpin; // 单次订单数
    QCheckBox *m_focusOnlyCheck; // 只刷关注发单人
    QCheckBox *m_priceFilterCheck; // 是否按价格过滤
    QLineEdit *m_priceMinEdit;
    QLineEdit *m_priceMaxEdit;
    QCheckBox *m_intervalCheck; // 启用间隔
    QSpinBox *m_intervalSpin;   // 间隔毫秒
    QComboBox *m_fingerprintCombo; // 主账号指纹选择

    
    // ===== 新增：高级/普通关键词筛选 UI =====
    QLineEdit *m_filterKeywordEdit; // 普通关键词（空格分隔）
    QLineEdit *m_excludeKeywordEdit; // 排除关键词（空格分隔，一直生效）
    QCheckBox *m_advancedModeCheck; // 勾选后启用高级筛选
    QTableWidget *m_advancedTable;   // 高级筛选表格：关键词 + 最低价
    
    // 支付密码输入
    QLineEdit *m_payPasswordEdit;
    
    QPushButton *m_loginButton;
    QPushButton *m_batchLoginButton;
    QPushButton *m_refreshButton;
    QPushButton *m_stopButton;
    QPushButton *m_loadAccountsButton;
    QPushButton *m_clearLogButton;
    QPushButton* m_mainAccountRefreshButton;


    // QPushButton *m_testNewEngineButton;  // 新架构测试按钮 - 暂时注释掉
    
    // 数据
    QList<AccountInfo> m_accounts;
    QList<OrderInfo> m_orders;
    QList<OrderInfo> m_mainAccountOrders;
    QMutex m_dataMutex;
    QSet<QString> m_seenOrderIds;      // 已出现过的订单流水号
    // 记录每个订单上次看到的价格，用于判断价格大幅上调的新单
    QHash<QString, double> m_lastPrice;
    // 本次运行内已失败(>=3次)的订单，避免重复尝试
    QSet<QString> m_failedOrderIds;
    
    // API和线程
    OrderAPI *m_api;
    QThread *m_apiThread;
    // 多账号并行解析：账号Key -> watcher & 排队包
    QHash<QString, QFutureWatcher<RefreshParseResult>*> m_watcherMap;
    QHash<QString, QQueue<QJsonArray>> m_pendingMap;
    QTimer *m_refreshTimer;
    QTimer *m_mainAcctTimer; // 主账号定时刷新
    bool m_mainAcctWaiting = false;
    bool m_mainAcctTimerWasActive = false; // 记录批量登录前定时器状态
    
    // 状态
    bool m_isRefreshing;
    int m_currentAccountIndex;
    int m_batchLoginIndex;
    bool m_isBatchLogin;
    bool m_isMainAccountRefreshing = false;
    bool m_waitingRefresh = false; // 标记是否等待当前账号刷新完成
    bool m_focusOnly = false;
    // 排除关键词缓存
    QString m_normalExcludeKeywords;
    QString m_advancedExcludeKeywords;
    bool m_showOrderSummary = false;
    QString m_currentRefreshingLabel; // 当前正在刷新账号标签（主账号或用户名）
    // 接单队列
    QList<AcceptEntry> m_acceptQueue;
    bool m_acceptInProgress = false;

    // ===== 自动停止设置 =====
    QSpinBox *m_acceptTargetSpin = nullptr; // 目标成功接单数
    int m_acceptSuccessCount = 0;           // 已成功数量
    QCheckBox *m_stopAfterTargetCheck = nullptr; // 是否启用停抢

    void batchLoginNext();
    void finalizeBatchLogin();  // 新增：并发批量登录完成后的收尾
    void stopBatchLogin();      // 新增：强制停止批量登录

    // 刷新逻辑辅助函数
    void refreshMainAccount();
    bool hasLoggedInSubAccounts();
    void refreshSubAccount(int index);
    void startNextRound();
    void showRefreshAccountList();
    bool buildPriceStr(QString& priceStr);
    
    // ===== 新增：订单过滤 =====
    bool orderMatchesFilter(const QJsonObject &orderObj);
    
    MainAccountInfo m_mainAccount;
    // 已删除主账号订单表格相关成员和函数
    double m_mainAccountBalance = 0.0;
    bool m_loadingSettings = false;
    // 自动接单
    void attemptAcceptOrder(const QJsonObject &orderObj, bool manual=false);
    void startBatchRefreshLoop();
    int m_pendingLoginCount = 0; // 并发批量登录，尚未返回的请求数
    void sendNextAccept();
    void insertOrdersToTable(const QVector<OrderInfo>& orders);
    void continueAfterParse();

    // 关键词过滤优化数据结构
    QSet<QChar> m_commonChars;              // 常见字符集合
    QSet<QString> m_singleCharExcludeNormal;   // 普通模式单字排除关键词
    QSet<QString> m_multiCharExcludeNormal;    // 普通模式多字排除关键词
    QSet<QString> m_singleCharExcludeAdvanced; // 高级模式单字排除关键词
    QSet<QString> m_multiCharExcludeAdvanced;  // 高级模式多字排除关键词
    bool m_filterOptimized = false;         // 标记是否已进行过优化
    bool m_filterLocked = false;            // 刷新时锁定关键词结构，提高性能
    
    // 高级关键词分组优化
    QHash<QString, QVector<KeywordPriceEntry>> m_keywordGroups; // 分组的关键词
    QHash<QString, bool> m_groupHasMinus;   // 记录每个组是否含有"-"关键词
    QStringList m_groupNames;               // 所有分组名称列表
    
    // 分组统计显示
    QTableWidget *m_groupStatsTable;        // 分组统计表格

    void optimizeKeywordFilters();          // 关键词过滤优化函数
    void autoGroupKeywords();               // 自动分组关键词函数
    void updateGroupStatsTable();           // 更新分组统计表格
    void updateExclusionMap(const QString &formatString); // 更新额外排除词映射
    void updateAdvancedTableColors();       // 更新高级表格行颜色

    // 额外排除关键词处理
    QThread *m_extraExclusionThread = nullptr;
    FilterWorker *m_extraExclusionWorker = nullptr;
    QAtomicInt m_extraExclusionResult;
    QAtomicInt m_extraExclusionFinished;
    QString m_exclusionMapFormat; // 存储额外排除词格式

    // 账号导入相关的辅助函数
    bool parseAccountLine(const QString &line, AccountInfo &account);
    bool parseProxyInfo(const QStringList &parts, AccountInfo &account);
    bool parseSlashSeparatedProxy(const QString &proxyField, AccountInfo &account);
    bool parseCommaSeparatedProxy(const QStringList &parts, AccountInfo &account);
    bool parseHttpProxy(const QStringList &parts, AccountInfo &account);
    bool isDuplicateAccount(const AccountInfo &account);
    
    // 上下文菜单相关的辅助函数
    void addClearMenuActions(QMenu &menu, QTableWidget *table);
    void extractOrderInfo(QTableWidget *table, int row, QString &pubId, QString &serialNo);
    void addOrderSpecificActions(QMenu &menu, const QString &pubId, const QString &serialNo);
    QSet<QString> getSelectedPublisherIds(QTableWidget *table);
    void addBatchActions(QMenu &menu, const QSet<QString> &selectedPubIds);
    void performFocusUser(const QString &pubId, const QString &serialNo);
    void performUnfocusUser(const QString &pubId);
    void performBlackUser(const QString &pubId);
    void performUnblackUser(const QString &pubId);
    void performBatchBlackUsers(const QSet<QString> &selectedPubIds);
    void performBatchUnblackUsers(const QSet<QString> &selectedPubIds);
    
    // 主账号操作结果处理函数
    void onMainAccountFocusResult(const QString &fid, bool ok, const QString &msg);
    void onMainAccountCancelFocusResult(const QString &fid, bool ok, const QString &msg);
    void onMainAccountBlackUserResult(const QString &uid, bool ok, const QString &msg);
    void onMainAccountRemoveBlackUserResult(const QString &uid, bool ok, const QString &msg);

    // 测试代理功能
    void testProxyFunctionality();
};

#endif // MAINWINDOW_H 