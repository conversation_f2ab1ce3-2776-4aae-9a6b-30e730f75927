# 🔍 主账号vs子账号详细请求对比

## 🚨 **已发现的关键差异**

### 1. **AppVer版本差异** ⭐ **已修复**
```
主账号: "AppVer", "4.6.4"
子账号: "AppVer", "5.0.6"  → 已修复为 "4.6.4"
```

### 2. **浏览器指纹差异** ⭐ **已修复**
```
主账号: WECHAT_BROWSER 指纹
子账号: QUARK_BROWSER 指纹  → 已修复为 WECHAT_BROWSER
```

### 3. **网络引擎路径差异**
```
主账号: executeNetworkRequest() → executeUltraFastTLSRequest() → m_ultraFastTLS
子账号: 直接调用 → m_subAccountUltraFastTLS
```

## 🔍 **需要进一步验证的差异**

### A. **请求头完整性**
**UltraFastTLS固定请求头**：
```http
User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/123.0.6312.80 Mobile Safari/537.36 Quark/7.14.5.880
Content-Type: application/x-www-form-urlencoded
X-Requested-With: com.quark.browser
Accept: */*
Accept-Language: zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7
Accept-Encoding: identity
Origin: https://m.dailiantong.com
Referer: https://m.dailiantong.com/
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
sec-ch-ua: "Android WebView";v="123", "Not:A-Brand";v="8", "Chromium";v="123"
sec-ch-ua-platform: "Android"
sec-ch-ua-mobile: ?1
```

**问题**：主账号和子账号都使用相同的UltraFastTLS请求头，但：
- 浏览器指纹设置为WECHAT_BROWSER
- 请求头却是夸克浏览器的头部
- 这种不匹配可能导致服务器识别异常

### B. **POST数据参数对比**
**主账号参数**：
```
IsPub=0&GameID=xxx&ZoneID=0&ServerID=0&SearchStr=&STier=&ETier=&Sort_Str=&PageIndex=1&PageSize=50&Price_Str=xxx&PubCancel=0&SettleHour=0&FilterType=0&PGType=0&Focused=xxx&OrderType=0&PubRecommend=0&Score1=0&Score2=0&UserID=xxx&TimeStamp=xxx&Ver=1.0&AppVer=4.6.4&AppOS=WebApp%20IOS&AppID=webapp&Token=xxx&Sign=xxx
```

**子账号参数**：
```
IsPub=0&GameID=xxx&ZoneID=0&ServerID=0&SearchStr=&STier=&ETier=&Sort_Str=&PageIndex=1&PageSize=50&Price_Str=xxx&PubCancel=0&SettleHour=0&FilterType=0&PGType=0&Focused=xxx&OrderType=0&PubRecommend=0&Score1=0&Score2=0&UserID=xxx&TimeStamp=xxx&Ver=1.0&AppVer=4.6.4&AppOS=WebApp%20IOS&AppID=webapp&Token=xxx&Sign=xxx
```

**现在应该完全一致** ✅

## 🛠️ **已应用的修复**

### 1. **统一AppVer版本**
```cpp
// 子账号现在使用与主账号相同的版本
{"AppVer", "4.6.4"} // 与主账号保持一致
```

### 2. **统一浏览器指纹**
```cpp
// 子账号现在使用与主账号相同的指纹
m_subAccountUltraFastTLS->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
```

### 3. **增强调试日志**
```cpp
// 主账号调试信息
emit debugLog(QString("🚀 [主账号] 开始执行请求: %1").arg(url));
emit debugLog(QString("📋 [主账号] 关键参数: %1").arg(part));

// 子账号调试信息
emit debugLog(QString("🚀 开始执行子账号请求: %1").arg(fullUrl));
emit debugLog(QString("📋 关键参数: %1").arg(part));
```

## 🎯 **预期测试结果**

重新编译运行后，应该能看到：

### ✅ **参数对比日志**
```
[主账号] 📋 关键参数: AppVer=4.6.4
[主账号] 📋 关键参数: UserID=12345
[主账号] 📋 关键参数: Token=abcdef

[***********] 📋 关键参数: AppVer=4.6.4
[***********] 📋 关键参数: UserID=12345  
[***********] 📋 关键参数: Token=abcdef
```

### ✅ **SSL握手成功**
```
[***********] [子账号TLS] ✅ SOCKS5代理连接成功: server.dailiantong.com.cn:443
[***********] [子账号TLS] 🔐 开始SSL握手 (超时: 20秒, 代理模式: 是)
[***********] [子账号TLS] ✅ SSL握手成功 (耗时: 1234ms)
[***********] 📥 子账号请求响应长度: 1234
```

## 🔧 **如果仍然失败的可能原因**

### 1. **Token差异**
- 主账号和子账号使用不同的Token
- 需要验证Token是否有效

### 2. **签名算法差异**
- 主账号和子账号的签名计算可能不同
- 需要对比Sign参数

### 3. **服务器端限制**
- 服务器可能对同时使用多个相同AppVer的请求有限制
- 可能需要错开请求时间

### 4. **代理服务器问题**
- 某些代理服务器可能对特定请求头敏感
- 可能需要测试不同的代理服务器

## 🚀 **立即测试步骤**

1. **重新编译程序**
2. **批量登录账号**
3. **点击"开始刷新"**
4. **对比主账号和子账号的详细日志**：
   - 参数是否完全一致
   - SSL握手是否成功
   - 响应长度是否正常

## 🎉 **预期效果**

这次修复应该能解决：
- ✅ AppVer版本不一致问题
- ✅ 浏览器指纹不匹配问题  
- ✅ 提供详细的对比日志

如果子账号仍然失败，现在我们有了详细的对比日志，可以精确定位剩余的差异！

---

**重要**：这是一个全面的对比修复，现在主账号和子账号应该使用完全相同的参数和配置！
